import torch
from torch.utils.data import DataLoader
from transformers import Gemma3nProcessor
from PIL import Image
from typing import List, Tuple

from .dataset import FaceCropDataset


def create_face_dataloaders(
    dataset: FaceCropDataset,
    processor: Gemma3nProcessor,
    batch_size: int,
    shuffle: bool = True,
    num_workers: int = 4
) -> DataLoader:
    """
    Creates a DataLoader for the FaceCropDataset.
    to process images with Gemm3nProcessor.

    Args:
        dataset (Dataset): The dataset to load.
        processor (Gemma3nProcessor): The processor to use for image processing.
        batch_size (int): The batch size.
        shuffle (bool): Whether to shuffle the data.
        num_workers (int): Number of workers for data loading.

    Returns:
        DataLoader: The DataLoader for the dataset.
    """
    def collate_fn(batch: List[Tuple[Image.Image, int]]):
        """
        Custom collate function to process a batch of images and labels.
        Uses the Gemma3nProcessor to prepare inputs for the model.
        Args:
            batch (List[Tuple[Image.Image, int]]): List of (image, label) tuples.
        
        Returns:
            Dict: A dictionary containing the processed inputs and labels.
        """
        images = [item[0] for item in batch]
        labels = torch.tensor([item[1] for item in batch], dtype=torch.long)

        # Process images using Gemma3nProcessor
        # The processor handles resizing, normalization, and creating pixel_values
        # adn input_ids with image soft tokens.
        # We provide a dummy text input as processor expects it for multimodal inputs.
        # The image_token will be replaced by the full image sequence internally.
        processed_inputs = processor(
            images=images,
            text=["<image>" for _ in images],  # Dummy text with image token placeholder
            return_tensors="pt",
        )

        # The processed_inputs will contain pixel_values and input_ids with soft tokens
        # And attention_mask
        # we also need to return labels.
        return {
            "pixel_values": processed_inputs["pixel_values"],
            "input_ids": processed_inputs["input_ids"],
            "attention_mask": processed_inputs["attention_mask"],
            "labels": labels,
        }
    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        collate_fn=collate_fn
    )
