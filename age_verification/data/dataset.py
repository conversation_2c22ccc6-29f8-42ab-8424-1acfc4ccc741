import json
import os
from PIL import Image
from torch.utils.data import Dataset


class FaceCropDataset(Dataset):
    def __init__(self, manifest_file: str, image_base_dir: str, age_threshold: int = 30):
        """
        Args:
            manifest_file (str): Path to the manifest file containing image paths and ages.
                                Expected format: [{"image_path": "path/to/image.jpg", "age": 25}, ...]
            image_base_dir (str): Base directory for the images.
            age_threshold (int): Age threshold for filtering images.
        """
        with open(manifest_file, 'r') as f:
            self.data = json.load(f)
        self.image_base_dir = image_base_dir
        self.age_threshold = age_threshold
    
    def __len__(self) -> int:
        return len(self.data)

    def __getitem__(self, idx):
        item = self.data[idx]
        image_path = os.path.join(self.image_base_dir, item["image_path"])
        image = Image.open(image_path).convert("RGB")
        
        age = item.get("age")
        # Label: 0 for underage, 1 for adult
        label = 0 if age is not None and age < self.age_threshold else 1

        return image, label
